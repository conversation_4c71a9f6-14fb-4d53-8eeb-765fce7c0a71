document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const noteContent = document.getElementById('note-content');
    const newNoteBtn = document.getElementById('new-note');
    const saveNoteBtn = document.getElementById('save-note');
    const downloadNoteBtn = document.getElementById('download-note');
    const fontSizeInput = document.getElementById('font-size');
    const wordCountEl = document.getElementById('word-count');
    const charCountEl = document.getElementById('char-count');
    
    // Load saved note if exists
    const savedNote = localStorage.getItem('savedNote');
    if (savedNote) {
        noteContent.innerHTML = savedNote;
        updateCounts();
    }
    
    // Set font size from saved preference or default
    const savedFontSize = localStorage.getItem('fontSize') || 16;
    fontSizeInput.value = savedFontSize;
    noteContent.style.fontSize = `${savedFontSize}px`;
    
    // Event Listeners
    newNoteBtn.addEventListener('click', () => {
        if (noteContent.textContent.trim() !== '' && confirm('Are you sure you want to create a new note? Current content will be lost.')) {
            noteContent.innerHTML = '';
            updateCounts();
        } else if (noteContent.textContent.trim() === '') {
            noteContent.innerHTML = '';
        }
    });
    
    saveNoteBtn.addEventListener('click', () => {
        localStorage.setItem('savedNote', noteContent.innerHTML);
        alert('Note saved successfully!');
    });
    
    downloadNoteBtn.addEventListener('click', () => {
        if (noteContent.textContent.trim() === '') {
            alert('Cannot download an empty note!');
            return;
        }
        
        const blob = new Blob([noteContent.innerHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        
        a.href = url;
        a.download = 'my-note.html';
        document.body.appendChild(a);
        a.click();
        
        // Cleanup
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    });
    
    fontSizeInput.addEventListener('change', () => {
        const fontSize = fontSizeInput.value;
        noteContent.style.fontSize = `${fontSize}px`;
        localStorage.setItem('fontSize', fontSize);
    });
    
    noteContent.addEventListener('input', updateCounts);
    
    // Helper Functions
    function updateCounts() {
        const text = noteContent.textContent;
        const wordCount = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
        const charCount = text.length;
        
        wordCountEl.textContent = `Words: ${wordCount}`;
        charCountEl.textContent = `Characters: ${charCount}`;
    }
    
    // Auto-save every 30 seconds
    setInterval(() => {
        if (noteContent.textContent.trim() !== '') {
            localStorage.setItem('savedNote', noteContent.innerHTML);
            console.log('Auto-saved note');
        }
    }, 30000);
});