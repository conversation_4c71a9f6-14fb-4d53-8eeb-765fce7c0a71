# Simple Notepad

A lightweight, browser-based notepad application with a clean and modern interface.

## Features

- Create and edit text notes
- Auto-save functionality (every 30 seconds)
- Manual save option
- Download notes as text files
- Adjust font size
- Word and character count
- Responsive design for various screen sizes

## Usage

1. Open `index.html` in any modern web browser
2. Start typing in the text area
3. Use the toolbar buttons to:
   - Create a new note
   - Save the current note to browser storage
   - Download the note as a text file
4. Adjust the font size using the number input in the toolbar

## Technical Details

This application is built with:
- HTML5
- CSS3
- Vanilla JavaScript

It uses the browser's localStorage API to save notes locally on your device.

## Getting Started

No installation or setup is required. Simply open the `index.html` file in your web browser to start using the notepad.

```
open index.html
```

Or you can use a local development server if you prefer.