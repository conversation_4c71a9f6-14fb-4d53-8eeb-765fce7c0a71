<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Notepad</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Story AI</h1>
        </div>
        <div class="toolbar">
            <button id="new-note">New</button>
            <button id="save-note">Save</button>
            <button id="download-note">Download</button>
            <button id="ai-write">AI Write</button>
            <div class="font-size-control">
                <label for="font-size">Font Size:</label>
                <input type="number" id="font-size" min="8" max="36" value="16">
            </div>
        </div>
        <div class="editor-container">
            <!--<textarea id="note-content" placeholder="Start typing your note here..."></textarea>-->
            <div id="note-content" contenteditable="true" placeholder="Start typing your note here..."></div>
        </div>
        <div class="status-bar">
            <span id="word-count">Words: 0</span>
            <span id="char-count">Characters: 0</span>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>