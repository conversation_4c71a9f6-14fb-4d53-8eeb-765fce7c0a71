* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 80%;
    max-width: 900px;
    height: 80vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    background-color: #4a90e2;
    color: white;
    padding: 15px 20px;
    text-align: center;
}

.header h1 {
    font-size: 24px;
    font-weight: 500;
}

.toolbar {
    padding: 10px 20px;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.toolbar button {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 8px 15px;
    margin-right: 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.toolbar button:hover {
    background-color: #357abd;
}

.font-size-control {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.font-size-control label {
    margin-right: 8px;
    color: #555;
}

.font-size-control input {
    width: 50px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.editor-container {
    flex: 1;
    padding: 10px;
    overflow: hidden;
}

#note-content {
    width: 100%;
    height: 100%;
    border: none;
    resize: none;
    padding: 10px;
    font-size: 16px;
    line-height: 1.5;
    outline: none;
}

.status-bar {
    padding: 8px 20px;
    background-color: #f0f0f0;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    color: #555;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 90vh;
    }
    
    .toolbar {
        flex-wrap: wrap;
    }
    
    .font-size-control {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
}